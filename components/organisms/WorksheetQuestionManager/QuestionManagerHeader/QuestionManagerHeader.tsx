'use client';

import React, { useState } from 'react';
import { Plus, Trash2, CheckSquare, Square, MoreVertical, Upload, Edit, Copy } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface QuestionManagerHeaderProps {
  totalQuestions: number;
  selectedCount: number;
  canAddQuestion: boolean;
  canBulkOperations: boolean;
  onAddQuestion: () => void;
  onBulkDelete: () => void;
  onBulkAdd?: () => void;
  onBulkUpdate?: () => void;
  onBulkDuplicate?: () => void;
  onSelectAll: (selected: boolean) => void;
  isLoading: boolean;
  className?: string;
}

export const QuestionManagerHeader: React.FC<QuestionManagerHeaderProps> = ({
  totalQuestions,
  selectedCount,
  canAddQuestion,
  canBulkOperations,
  onAddQuestion,
  onBulkDelete,
  onBulkAdd,
  onBulkUpdate,
  onBulkDuplicate,
  onSelectAll,
  isLoading,
  className
}) => {
  const [showBulkMenu, setShowBulkMenu] = useState(false);
  const allSelected = selectedCount === totalQuestions && totalQuestions > 0;
  const someSelected = selectedCount > 0;

  return (
    <div className={cn(
      "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-base-100 border-b border-base-300",
      className
    )}>
      {/* Left side - Title and stats */}
      <div className="flex flex-col gap-1">
        <h2 className="text-xl font-semibold text-base-content">
          Questions ({totalQuestions})
        </h2>
        {someSelected && (
          <p className="text-sm text-base-content/70">
            {selectedCount} of {totalQuestions} selected
          </p>
        )}
      </div>

      {/* Right side - Actions */}
      <div className="flex flex-wrap items-center gap-2">
        {/* Bulk selection controls */}
        {canBulkOperations && totalQuestions > 0 && (
          <div className="flex items-center gap-2">
            <button
              onClick={() => onSelectAll(!allSelected)}
              className="btn btn-ghost btn-sm"
              disabled={isLoading}
              aria-label={allSelected ? "Deselect all questions" : "Select all questions"}
            >
              {allSelected ? (
                <CheckSquare className="w-4 h-4" />
              ) : (
                <Square className="w-4 h-4" />
              )}
              <span className="hidden sm:inline">
                {allSelected ? 'Deselect All' : 'Select All'}
              </span>
            </button>

            {/* Bulk operations menu */}
            {someSelected && (
              <div className="relative">
                <div className="dropdown dropdown-end">
                  <div tabIndex={0} role="button" className="btn btn-outline btn-sm">
                    <MoreVertical className="w-4 h-4" />
                    <span className="hidden sm:inline">
                      Bulk Actions ({selectedCount})
                    </span>
                    <span className="sm:hidden">
                      Actions
                    </span>
                  </div>
                  <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow border border-base-300">
                    {onBulkUpdate && (
                      <li>
                        <button
                          onClick={() => {
                            onBulkUpdate();
                            setShowBulkMenu(false);
                          }}
                          disabled={isLoading}
                          className="flex items-center gap-2"
                        >
                          <Edit className="w-4 h-4" />
                          Update Selected
                        </button>
                      </li>
                    )}
                    {onBulkDuplicate && (
                      <li>
                        <button
                          onClick={() => {
                            onBulkDuplicate();
                            setShowBulkMenu(false);
                          }}
                          disabled={isLoading}
                          className="flex items-center gap-2"
                        >
                          <Copy className="w-4 h-4" />
                          Duplicate Selected
                        </button>
                      </li>
                    )}
                    <li>
                      <button
                        onClick={() => {
                          onBulkDelete();
                          setShowBulkMenu(false);
                        }}
                        disabled={isLoading}
                        className="flex items-center gap-2 text-error hover:bg-error/10"
                      >
                        <Trash2 className="w-4 h-4" />
                        Delete Selected
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Add question buttons */}
        {canAddQuestion && (
          <div className="flex items-center gap-2">
            <Button
              variant="primary"
              size="sm"
              onClick={onAddQuestion}
              disabled={isLoading}
              className="btn-primary"
            >
              <Plus className="w-4 h-4" />
              <span className="hidden sm:inline">Add Question</span>
              <span className="sm:hidden">Add</span>
            </Button>

            {/* Bulk add button for advanced users */}
            {canBulkOperations && onBulkAdd && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBulkAdd}
                disabled={isLoading}
                className="btn-outline"
              >
                <Upload className="w-4 h-4" />
                <span className="hidden sm:inline">Bulk Add</span>
                <span className="sm:hidden">Bulk</span>
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
