'use client';

import React, { useState, useCallback } from 'react';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { QuestionList } from './QuestionList/QuestionList';
import { QuestionFormModal } from './QuestionFormModal/QuestionFormModal';
import { QuestionManagerHeader } from './QuestionManagerHeader/QuestionManagerHeader';
import { DeleteQuestionModal } from './DeleteQuestionModal/DeleteQuestionModal';
import { BulkDeleteModal } from './BulkDeleteModal/BulkDeleteModal';
import { cn } from '@/utils/cn';

export interface WorksheetQuestionManagerProps {
  worksheetId: string;
  initialQuestions: IWorksheetQuestion[];
  userRole: string;
  userSchoolId?: string;
  isReadOnly?: boolean;
  className?: string;
}

export const WorksheetQuestionManager: React.FC<WorksheetQuestionManagerProps> = ({
  worksheetId,
  initialQuestions,
  userRole,
  userSchoolId,
  isReadOnly = false,
  className
}) => {
  // State management
  const [questions, setQuestions] = useState<IWorksheetQuestion[]>(initialQuestions);
  const [isAddingQuestion, setIsAddingQuestion] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<IWorksheetQuestion | null>(null);
  const [deletingQuestion, setDeletingQuestion] = useState<IWorksheetQuestion | null>(null);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Permission checks
  const canAddQuestion = !isReadOnly && ['admin', 'school_manager', 'teacher', 'independent_teacher'].includes(userRole);
  const canEditQuestion = !isReadOnly && ['admin', 'school_manager', 'teacher', 'independent_teacher'].includes(userRole);
  const canDeleteQuestion = !isReadOnly && ['admin', 'school_manager', 'independent_teacher'].includes(userRole);
  const canReorderQuestions = !isReadOnly && ['admin', 'school_manager', 'teacher', 'independent_teacher'].includes(userRole);
  const canBulkOperations = !isReadOnly && ['admin', 'school_manager'].includes(userRole);

  // Question handlers
  const handleAddQuestion = useCallback(() => {
    setIsAddingQuestion(true);
  }, []);

  const handleEditQuestion = useCallback((question: IWorksheetQuestion) => {
    console.log('handleEditQuestion called with question:', question.id);
    setEditingQuestion(question);
  }, []);

  const handleDeleteQuestion = useCallback((question: IWorksheetQuestion) => {
    console.log('handleDeleteQuestion called with question:', question.id);
    setDeletingQuestion(question);
  }, []);

  const handleQuestionCreated = useCallback((newQuestion: IWorksheetQuestion) => {
    setQuestions(prev => [...prev, newQuestion]);
    setIsAddingQuestion(false);
  }, []);

  const handleQuestionUpdated = useCallback((updatedQuestion: IWorksheetQuestion) => {
    setQuestions(prev => 
      prev.map(q => q.id === updatedQuestion.id ? updatedQuestion : q)
    );
    setEditingQuestion(null);
  }, []);

  const handleQuestionDeleted = useCallback((questionId: string) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId));
    setDeletingQuestion(null);
  }, []);

  const handleQuestionsReordered = useCallback((reorderedQuestions: IWorksheetQuestion[]) => {
    setQuestions(reorderedQuestions);
  }, []);

  // Selection handlers
  const handleQuestionSelect = useCallback((questionId: string, selected: boolean) => {
    setSelectedQuestions(prev => 
      selected 
        ? [...prev, questionId]
        : prev.filter(id => id !== questionId)
    );
  }, []);

  const handleSelectAll = useCallback((selected: boolean) => {
    setSelectedQuestions(selected ? questions.map(q => q.id) : []);
  }, [questions]);

  const handleBulkDelete = useCallback(() => {
    if (selectedQuestions.length > 0) {
      setIsBulkDeleting(true);
    }
  }, [selectedQuestions]);

  const handleBulkDeleteComplete = useCallback((deletedIds: string[]) => {
    setQuestions(prev => prev.filter(q => !deletedIds.includes(q.id)));
    setSelectedQuestions([]);
    setIsBulkDeleting(false);
  }, []);

  // Get available question types based on user role
  const getAvailableQuestionTypes = useCallback(() => {
    const allTypes = [
      { value: 'MULTIPLE_CHOICE', label: 'Multiple Choice' },
      { value: 'TRUE_FALSE', label: 'True/False' },
      { value: 'FILL_IN_BLANK', label: 'Fill in the Blank' },
      { value: 'SHORT_ANSWER', label: 'Short Answer' },
      { value: 'ESSAY', label: 'Essay' },
      { value: 'MATCHING', label: 'Matching' },
      { value: 'ORDERING', label: 'Ordering' }
    ];

    // Admin and school managers have access to all types
    if (['admin', 'school_manager'].includes(userRole)) {
      return allTypes;
    }

    // Teachers and independent teachers have limited access
    return allTypes.filter(type => 
      ['MULTIPLE_CHOICE', 'TRUE_FALSE', 'FILL_IN_BLANK', 'SHORT_ANSWER'].includes(type.value)
    );
  }, [userRole]);

  return (
    <div className={cn("worksheet-question-manager w-full", className)}>
      {/* Header */}
      <QuestionManagerHeader
        totalQuestions={questions.length}
        selectedCount={selectedQuestions.length}
        canAddQuestion={canAddQuestion}
        canBulkOperations={canBulkOperations}
        onAddQuestion={handleAddQuestion}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        isLoading={isLoading}
      />

      {/* Question List */}
      <QuestionList
        questions={questions}
        selectedQuestions={selectedQuestions}
        canEdit={canEditQuestion}
        canDelete={canDeleteQuestion}
        canReorder={canReorderQuestions}
        canSelect={canBulkOperations}
        onEditQuestion={handleEditQuestion}
        onDeleteQuestion={handleDeleteQuestion}
        onSelectQuestion={handleQuestionSelect}
        onReorderQuestions={handleQuestionsReordered}
        worksheetId={worksheetId}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
      />

      {/* Add Question Modal */}
      <QuestionFormModal
        isOpen={isAddingQuestion}
        onClose={() => setIsAddingQuestion(false)}
        onSuccess={handleQuestionCreated}
        worksheetId={worksheetId}
        questionTypes={getAvailableQuestionTypes()}
        mode="create"
      />

      {/* Edit Question Modal */}
      {editingQuestion && (
        <QuestionFormModal
          isOpen={true}
          onClose={() => setEditingQuestion(null)}
          onSuccess={handleQuestionUpdated}
          worksheetId={worksheetId}
          questionTypes={getAvailableQuestionTypes()}
          mode="edit"
          initialData={editingQuestion}
        />
      )}

      {/* Delete Question Modal */}
      {deletingQuestion && (
        <DeleteQuestionModal
          isOpen={true}
          onClose={() => setDeletingQuestion(null)}
          onSuccess={handleQuestionDeleted}
          worksheetId={worksheetId}
          question={deletingQuestion}
        />
      )}

      {/* Bulk Delete Modal */}
      <BulkDeleteModal
        isOpen={isBulkDeleting}
        onClose={() => setIsBulkDeleting(false)}
        onSuccess={handleBulkDeleteComplete}
        worksheetId={worksheetId}
        questionIds={selectedQuestions}
        questionCount={selectedQuestions.length}
      />
    </div>
  );
};
