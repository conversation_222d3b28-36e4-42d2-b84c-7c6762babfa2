'use client';

import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { LoadingWorksheetScreen } from '@/components/molecules/LoadingWorksheetScreen/LoadingWorksheetScreen';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';
import { Button } from '@/components/atoms/Button/Button';
import Icon from '@/components/atoms/Icon';
import Link from 'next/link';
import { WorksheetGeneratingStatus } from '@/apis/worksheet';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { useWorksheetProgress } from '@/hooks/useWorksheetProgress';
import { useState, useEffect } from 'react';
import { PrintModal } from '@/components/molecules/PrintModal';
import { useSession } from 'next-auth/react';



type WorksheetProgressViewProps = {
  worksheetId: string;
  initialStatus: WorksheetGeneratingStatus;
  initialQuestions?: Question[];
  initialProgress?: ProgressData;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  schoolInfo?: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  };
};

export const WorksheetProgressView: React.FC<WorksheetProgressViewProps> = ({
  worksheetId,
  initialStatus,
  initialQuestions = [],
  initialProgress,
  worksheetInfo,
  schoolInfo,
}) => {
  const { status, questions: hookQuestions } = useWorksheetProgress({
    worksheetId,
    initialStatus,
    initialQuestions,
    initialProgress,
  });

  // State for print modal and question management
  const [isPrintModalOpen, setIsPrintModalOpen] = useState(false);
  const [localQuestions, setLocalQuestions] = useState<Question[]>(initialQuestions);

  // Update local questions when hook questions change
  useEffect(() => {
    setLocalQuestions(hookQuestions);
  }, [hookQuestions]);

  // Get user session for permissions
  const { data: session } = useSession();


  // If status is pending, show the loading screen with progress
  if (status === WorksheetGeneratingStatus.PENDING) {
    return (
      <LoadingWorksheetScreen
        worksheetId={worksheetId}
        initialQuestions={initialQuestions}
        initialProgress={initialProgress}
      />
    );
  }

  // If status is generated or error, show the completed worksheet
  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <div className="w-full max-w-5xl mx-auto">
          <div className="w-full">
            {status === WorksheetGeneratingStatus.ERROR ? (
              <div className="w-full p-8 text-center">
                <div className="text-error text-xl mb-4">
                  An error occurred while generating the worksheet
                </div>
                <div className="text-gray-500">
                  Please try again or contact support if the problem persists.
                </div>
              </div>
            ) : (
              <>
                {/* Enhanced Question View with drag-and-drop and delete functionality */}
                <QuestionListingView
                  questions={localQuestions}
                  containerClass="pb-[50px] lg:pb-20"
                  isHtmlContent
                  worksheetInfo={worksheetInfo}
                  worksheetId={worksheetId}
                  isReadOnly={!Boolean(session?.user?.role && ['admin', 'school_manager', 'independent_teacher'].includes(session.user.role))}
                  onQuestionsChange={(updatedQuestions) => {
                    // Update the local questions state when changes occur
                    setLocalQuestions(updatedQuestions);
                  }}
                />

                {/* Mobile-Responsive Bottom Action Bar - Minimal Design */}
                <div className="fixed bottom-[55px] left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40 lg:bottom-0 lg:left-[310px] lg:w-[calc(100vw-310px)]">
                  <div className="flex justify-between items-center h-[48px] lg:h-[60px] px-3 lg:px-6">
                    <div className="w-7 h-7 lg:w-9 lg:h-9 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors">
                      <Link href="/manage-worksheet">
                        <Icon
                          variant="chevron-down"
                          className="rotate-90"
                          size={2.5}
                        />
                      </Link>
                    </div>
                    <div className="flex gap-2 lg:gap-6 items-center">
                
                      <Button
                        onClick={() => setIsPrintModalOpen(true)}
                        className="px-3 py-1.5 h-8 lg:p-3 lg:h-10 w-fit text-xs lg:text-base font-medium"
                      >
                        Print
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Print Modal */}
                <PrintModal
                  isOpen={isPrintModalOpen}
                  onClose={() => setIsPrintModalOpen(false)}
                  questions={localQuestions}
                  worksheetInfo={worksheetInfo}
                  schoolInfo={schoolInfo}
                />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
